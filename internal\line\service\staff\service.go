package staff

import (
	"fmt"

	"digital-transformation-api/infrastructure"
	"digital-transformation-api/internal/enum/staff"
	staffdb "digital-transformation-api/internal/portal/port/staff-db"
	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/logger"
)

type service struct {
	staffDb staffdb.Port
}

func New(staffDb staffdb.Port) Service {
	return &service{
		staffDb: staffDb,
	}
}

func (s *service) CheckStaff(request *RegisterStaffRequest, rctx *contexts.RouteContext, l logger.Logger) (*CheckStaffResponse, errs.Error) {
	fmt.Printf("request: %v", request)
	fmt.Printf("rctx: %v", rctx)
	if err := infrastructure.Validate.Struct(request); err != nil {
		l.<PERSON>("failed when validate check staff request: %v", err)
		return nil, errs.NewBadRequestError()
	}

	// Get staff by employee_id and phone
	getResp, err := s.staffDb.GetByEmployeeIDAndPhone(&staffdb.GetByEmployeeIDAndPhoneRequest{
		EmployeeID: request.EmployeeID,
		Phone:      request.Phone,
	}, rctx, l)
	if err != nil {
		return nil, err
	}

	// Check if staff exists and is active
	if getResp.Staff == nil {
		return &CheckStaffResponse{
			Valid:   false,
			Message: "เข้าสู่ระบบไม่สำเร็จ",
		}, nil
	}

	// Check if staff is active
	if getResp.Staff.Status != staff.StatusActive {
		return &CheckStaffResponse{
			Valid:   false,
			Message: "Staff account is not active",
		}, nil
	}

	return &CheckStaffResponse{
		Valid:   true,
		Message: "Staff verification successful",
	}, nil
}
